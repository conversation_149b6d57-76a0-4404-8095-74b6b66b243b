import Image from 'next/image';
import { Image as Img } from 'lucide-react';
import { ChevronRight, Link } from 'lucide-react';
import { Separator } from '@/components/ui/separator';
import { url } from 'inspector';

// Enhanced project content array with all projects
const PROJECT_CONTENT = [
  {
    title: 'Agentic AI',
    description:
      'An AI-driven agents project that automates regular or complex tasks. This project focuses on creating intelligent agents that can understand user requirements and execute tasks autonomously, making complex workflows simple and efficient.',
    techStack: [
      'Python',
      'JavaScript',
      'AI/ML Libraries',
      'API Integration',
      'Task Automation',
      'Natural Language Processing'
    ],
    date: '2024-2025',
    links: [
      {
        name: 'GitHub',
        url: 'https://github.com/mdtalib2003/agentic-ai',
      }
    ],
    images: [
      {
        src: '/agentic-ai-1.png',
        alt: 'Agentic AI dashboard',
      },
      {
        src: '/agentic-ai-2.png',
        alt: 'AI agent workflow',
      }
    ],
  },
  {
    title: 'Algorithmic Trading Platform',
    description:
      "Led the functional development of an advanced algorithmic trading platform with sophisticated data visualization and charting capabilities. The platform enables real-time market data visualization and execution of automated trading strategies with advanced analytics.",
    techStack: ['React.js', 'Node.js', 'WebSockets', 'Chart.js', 'Real-time APIs', 'Financial Data APIs'],
    date: '2025',
    links: [
      {
        name: 'Company',
        url: 'https://nextitsolutions.com',
      }
    ],
    images: [
      {
        src: '/trading-platform-1.png',
        alt: 'Trading platform dashboard',
      },
      {
        src: '/trading-platform-2.png',
        alt: 'Real-time charts and analytics',
      }
    ],
  },
  {
    title: 'Event Management Platform',
    description:
      "Engineered high-performance interactive web components for a sophisticated event management platform. Optimized platform performance by implementing iterative improvements including video compression, asset optimization, caching strategies, and performance monitoring.",
    techStack: [
      'React.js',
      'JavaScript',
      'Performance Optimization',
      'Video Compression',
      'Caching Strategies',
      'Asset Optimization'
    ],
    date: '2024-2025',
    links: [
      {
        name: 'Company',
        url: 'https://gokapture.com',
      }
    ],
    images: [
      {
        src: '/event-platform-1.png',
        alt: 'Event management dashboard',
      },
      {
        src: '/event-platform-2.png',
        alt: 'Interactive components',
      }
    ],
  },
  {
    title: 'Delivery Tracking App',
    description:
      'Architected and delivered a real-time delivery tracking application using route data storage and offline caching. Engineered seamless synchronization with backend database upon reconnection, ensuring reliable tracking even in poor network conditions.',
    techStack: ['React Native', 'Node.js', 'MongoDB', 'Real-time APIs', 'Offline Caching', 'GPS Integration'],
    date: '2024',
    links: [
      {
        name: 'GitHub',
        url: 'https://github.com/mdtalib2003/delivery-tracking',
      }
    ],
    images: [
      {
        src: '/delivery-app-1.png',
        alt: 'Delivery tracking interface',
      },
      {
        src: '/delivery-app-2.png',
        alt: 'Real-time route tracking',
      }
    ],
  },
  {
    title: 'Full Stack Web Projects',
    description:
      "Collection of various full-stack web development projects showcasing modern web technologies, responsive design, and efficient backend solutions. These projects demonstrate proficiency in both frontend and backend development.",
    techStack: [
      'React.js',
      'Node.js',
      'Express.js',
      'MongoDB',
      'JavaScript',
      'HTML/CSS'
    ],
    date: '2023-2024',
    links: [
      {
        name: 'GitHub Portfolio',
        url: 'https://github.com/mdtalib2003',
      }
    ],
    images: [
      {
        src: '/fullstack-1.png',
        alt: 'Full stack project showcase',
      },
      {
        src: '/fullstack-2.png',
        alt: 'Web application interface',
      }
    ],
  },
];

// Define interface for project prop
interface ProjectProps {
  title: string;
  description?: string;
  techStack?: string[];
  date?: string;
  links?: { name: string; url: string }[];
  images?: { src: string; alt: string }[];
}

const ProjectContent = ({ project }: { project: ProjectProps }) => {
  // Find the matching project data
  const projectData = PROJECT_CONTENT.find((p) => p.title === project.title);

  if (!projectData) {
    return <div>Project details not available</div>;
  }

  return (
    <div className="space-y-10">
      {/* Header section with description */}
      <div className="rounded-3xl bg-[#F5F5F7] p-8 dark:bg-[#1D1D1F]">
        <div className="space-y-6">
          <div className="flex items-center gap-2 text-sm text-neutral-500 dark:text-neutral-400">
            <span>{projectData.date}</span>
          </div>

          <p className="text-secondary-foreground font-sans text-base leading-relaxed md:text-lg">
            {projectData.description}
          </p>

          {/* Tech stack */}
          <div className="pt-4">
            <h3 className="mb-3 text-sm tracking-wide text-neutral-500 uppercase dark:text-neutral-400">
              Technologies
            </h3>
            <div className="flex flex-wrap gap-2">
              {projectData.techStack.map((tech, index) => (
                <span
                  key={index}
                  className="rounded-full bg-neutral-200 px-3 py-1 text-sm text-neutral-800 dark:bg-neutral-800 dark:text-neutral-200"
                >
                  {tech}
                </span>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Links section */}
      {projectData.links && projectData.links.length > 0 && (
        <div className="mb-24">
          <div className="px-6 mb-4 flex items-center gap-2">
            <h3 className="text-sm tracking-wide text-neutral-500 dark:text-neutral-400">
              Links
            </h3>
            <Link className="text-muted-foreground w-4" />
          </div>
          <Separator className="my-4" />
          <div className="space-y-3">
            {projectData.links.map((link, index) => (
                <a
                key={index}
                href={link.url}
                target="_blank"
                rel="noopener noreferrer"
                className="group bg-[#F5F5F7] flex items-center justify-between rounded-xl p-4 transition-colors hover:bg-[#E5E5E7] dark:bg-neutral-800 dark:hover:bg-neutral-700"
                >
                <span className="font-light capitalize">{link.name}</span>
                <ChevronRight className="h-4 w-4 transition-transform group-hover:translate-x-1" />
                </a>
            ))}
          </div>
        </div>
      )}

      {/* Images gallery */}
      {projectData.images && projectData.images.length > 0 && (
        <div className="space-y-6">
          <div className="grid grid-cols-1 gap-4">
            {projectData.images.map((image, index) => (
              <div
                key={index}
                className="relative aspect-video overflow-hidden rounded-2xl"
              >
                <Image
                  src={image.src}
                  alt={image.alt}
                  fill
                  className="object-cover transition-transform"
                />
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

// Main data export with updated content
export const data = [
  {
    category: 'AI Project',
    title: 'Agentic AI',
    src: '/agentic-ai-preview.png',
    content: <ProjectContent project={{ title: 'Agentic AI' }} />,
  },
  {
    category: 'FinTech',
    title: 'Algorithmic Trading Platform',
    src: '/trading-platform-preview.png',
    content: <ProjectContent project={{ title: 'Algorithmic Trading Platform' }} />,
  },
  {
    category: 'Event Management',
    title: 'Event Management Platform',
    src: '/event-platform-preview.png',
    content: <ProjectContent project={{ title: 'Event Management Platform' }} />,
  },
  {
    category: 'Mobile App',
    title: 'Delivery Tracking App',
    src: '/delivery-app-preview.png',
    content: <ProjectContent project={{ title: 'Delivery Tracking App' }} />,
  },
  {
    category: 'Web Development',
    title: 'Full Stack Web Projects',
    src: '/fullstack-preview.png',
    content: <ProjectContent project={{ title: 'Full Stack Web Projects' }} />,
  },
];
