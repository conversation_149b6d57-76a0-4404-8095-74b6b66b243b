import { tool } from 'ai';
import { z } from 'zod';

export const getPresentation = tool({
  description:
    'This tool returns a concise personal introduction of <PERSON><PERSON><PERSON>. It is used to answer the question "Who are you?" or "Tell me about yourself"',
  inputSchema: z.object({}),
  execute: async () => {
    return {
      presentation:
        "I'm <PERSON><PERSON><PERSON>, a passionate fullstack developer from Bhopal, India. Currently working as Lead Technical Developer at Next IT Soln, building innovative applications with modern web technologies. I specialize in React.js, Node.js, and creating efficient solutions for real-world problems.",
    };
  },
});
