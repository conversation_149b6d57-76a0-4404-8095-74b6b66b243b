import { tool } from 'ai';
import { z } from 'zod';

export const getInternship = tool({
  description:
    "Gives a summary of my work experience and what kind of opportunities I'm looking for, plus my contact info. Use this tool when the user asks about my work experience or career opportunities.",
  inputSchema: z.object({}),
  execute: async () => {
    return `Here's my work experience and what I'm looking for 👇

**Current Experience:**
- 🚀 **Lead Technical Developer** at Next IT Soln (Feb 2025 - Present)
  - Leading algorithmic trading platform development with real-time data visualization
- 💻 **Frontend Developer** at GokApture Event Technology (Sep 2024 - Jan 2025)
  - Built high-performance interactive web components for event management platform
- 📱 **App Developer** at GapAcres Solutions (Feb 2024 - Aug 2024)
  - Developed delivery tracking app with offline capabilities and real-time sync

**What I'm looking for:**
- 🌍 **Location**: Remote work preferred, based in Bhopal, India 🇮🇳
- 🧑‍💻 **Focus**: Full-stack development, AI integration, modern web applications
- 🛠️ **Stack**: React.js, Node.js, TypeScript, Python, MongoDB, Next.js
- ✅ **What I bring**: Proven experience in building scalable applications, performance optimization, and innovative solutions

📬 **Contact me** via:
- Email: <EMAIL>
- LinkedIn: [linkedin.com/in/mdtalib2003](https://linkedin.com/in/mdtalib2003)
- GitHub: [github.com/mdtalib2003](https://github.com/mdtalib2003)

Let's build something amazing together! 🚀
    `;
  },
});
