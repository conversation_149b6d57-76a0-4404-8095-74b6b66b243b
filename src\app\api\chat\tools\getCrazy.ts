
import { tool } from "ai";
import { z } from "zod";


export const getCrazy = tool({
  description:
    "This tool shows my achievements and certifications. Use it when the user asks about achievements, certifications, hackathons, or awards.",
  inputSchema: z.object({}),
  execute: async () => {
    return `Here are my achievements and certifications 🏆

**Hackathons & Competitions:**
- 🥇 **Won LAKECITY HACK 2025** at JLU School Of Engineering & Technology
- 🎯 **Qualified for Energy Hackathon 2025** at IIT Delhi
- 🎯 **Qualified for HACKSTAY** at Microsoft Office

**Certifications:**
- 📜 **Full Stack Web Development** - Udemy

**Current Projects:**
- 🤖 **Agentic_AI** - Working on AI-driven agents that automate regular or complex tasks

These experiences have shaped my problem-solving skills and passion for innovation!`;
  },
});